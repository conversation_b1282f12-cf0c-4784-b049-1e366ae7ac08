import Head from 'next/head';
import { useState } from 'react';

export default function ClientDashboard() {
  const [clientData] = useState({
    name: 'Иван Петров',
    contractNumber: 'ДДУ-2024-001',
    apartment: 'ЖК "Солнечный", корп. 1, кв. 45',
    totalAmount: 5500000,
    paidAmount: 2200000,
    nextPayment: {
      amount: 275000,
      dueDate: '2024-02-15',
    },
  });

  const paymentProgress = (clientData.paidAmount / clientData.totalAmount) * 100;

  return (
    <>
      <Head>
        <title>PactCRM - Личный кабинет</title>
        <meta name="description" content="Личный кабинет покупателя недвижимости" />
      </Head>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Добро пожаловать, {clientData.name}
                </h1>
                <p className="text-sm text-gray-500 mt-1">
                  Договор: {clientData.contractNumber}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                  Связаться с менеджером
                </button>
                <button className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm">
                  Выйти
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {/* Property Info */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Ваша недвижимость
              </h3>
              <div className="flex items-center space-x-4">
                <div className="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500 text-xs">Фото</span>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-semibold text-gray-900">{clientData.apartment}</h4>
                  <p className="text-gray-600 mt-1">3 комнаты • 85 м² • 12 этаж</p>
                  <p className="text-gray-600">Срок сдачи: IV квартал 2024</p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">
                    ₽{clientData.totalAmount.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-500">Общая стоимость</p>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Progress */}
          <div className="bg-white shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Прогресс оплаты
              </h3>
              <div className="mb-4">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Оплачено: ₽{clientData.paidAmount.toLocaleString()}</span>
                  <span>{paymentProgress.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-green-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${paymentProgress}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-gray-600 mt-2">
                  <span>Остаток: ₽{(clientData.totalAmount - clientData.paidAmount).toLocaleString()}</span>
                  <span>₽{clientData.totalAmount.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Next Payment & Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Next Payment */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Следующий платеж
                </h3>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-lg font-semibold text-gray-900">
                        ₽{clientData.nextPayment.amount.toLocaleString()}
                      </p>
                      <p className="text-sm text-gray-600">
                        Срок: {clientData.nextPayment.dueDate}
                      </p>
                    </div>
                    <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm">
                      Оплатить
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Быстрые действия
                </h3>
                <div className="space-y-3">
                  <button className="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">История платежей</span>
                      <span className="text-gray-400">→</span>
                    </div>
                  </button>
                  <button className="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">Документы</span>
                      <span className="text-gray-400">→</span>
                    </div>
                  </button>
                  <button className="w-full text-left px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900">Поддержка</span>
                      <span className="text-gray-400">→</span>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Payments */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Последние платежи
              </h3>
              <div className="space-y-3">
                {[
                  { date: '2024-01-15', amount: 275000, status: 'Оплачено', method: 'Банковский перевод' },
                  { date: '2023-12-15', amount: 275000, status: 'Оплачено', method: 'Банковский перевод' },
                  { date: '2023-11-15', amount: 275000, status: 'Оплачено', method: 'Наличные' },
                ].map((payment, index) => (
                  <div key={index} className="flex items-center justify-between py-3 border-b border-gray-200 last:border-b-0">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        ₽{payment.amount.toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">{payment.date} • {payment.method}</p>
                    </div>
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      {payment.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Development Notice */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-blue-800">
                  Демо-версия личного кабинета
                </h3>
                <div className="mt-2 text-sm text-blue-700">
                  <p>
                    Это демонстрационная версия с тестовыми данными. В полной версии будут доступны все функции управления договором и платежами.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </>
  );
}
