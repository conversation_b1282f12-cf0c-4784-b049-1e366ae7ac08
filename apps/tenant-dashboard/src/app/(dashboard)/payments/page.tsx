/**
 * @file: payments/page.tsx
 * @description: Страница управления платежами
 * @dependencies: react, next, @pactcrm/ui, @pactcrm/supabase-client
 * @created: 2023-12-01
 */

"use client";

import React, { useState } from 'react';
import { PaymentsList } from '@/components/payments/PaymentsList';
import { PaymentForm } from '@/components/payments/PaymentForm';
import { PaymentDetails } from '@/components/payments/PaymentDetails';
import { Payment } from '@pactcrm/supabase-client';

// Типы для режимов отображения
type ViewMode = 'list' | 'create' | 'edit' | 'view' | 'register';

export default function PaymentsPage() {
  // Состояние для управления режимами отображения
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);

  // Обработчики событий
  const handlePaymentSelect = (payment: Payment) => {
    setSelectedPayment(payment);
    setViewMode('view');
  };

  const handlePaymentCreate = () => {
    setSelectedPayment(null);
    setViewMode('create');
  };

  const handlePaymentEdit = (payment: Payment) => {
    setSelectedPayment(payment);
    setViewMode('edit');
  };

  const handlePaymentView = (payment: Payment) => {
    setSelectedPayment(payment);
    setViewMode('view');
  };

  const handlePaymentRegister = (payment: Payment) => {
    setSelectedPayment(payment);
    setViewMode('register');
  };

  const handlePaymentDelete = async (payment: Payment) => {
    // TODO: Реализовать удаление с подтверждением
    console.log('Удаление платежа:', payment);
  };

  const handleFormSubmit = async (data: any) => {
    // TODO: Обработка успешного сохранения
    console.log('Сохранение платежа:', data);
    setViewMode('list');
  };

  const handleBack = () => {
    setSelectedPayment(null);
    setViewMode('list');
  };

  // Рендеринг в зависимости от режима
  const renderContent = () => {
    switch (viewMode) {
      case 'list':
        return (
          <PaymentsList
            onPaymentSelect={handlePaymentSelect}
            onPaymentCreate={handlePaymentCreate}
            onPaymentEdit={handlePaymentEdit}
            onPaymentView={handlePaymentView}
            onPaymentRegister={handlePaymentRegister}
            onPaymentDelete={handlePaymentDelete}
          />
        );

      case 'create':
        return (
          <PaymentForm
            mode="create"
            onSubmit={handleFormSubmit}
            onCancel={handleBack}
            title="Создание платежа"
            submitText="Создать платеж"
          />
        );

      case 'edit':
        return (
          <PaymentForm
            paymentId={selectedPayment?.id}
            mode="edit"
            onSubmit={handleFormSubmit}
            onCancel={handleBack}
            title="Редактирование платежа"
            submitText="Сохранить изменения"
          />
        );

      case 'register':
        return (
          <PaymentForm
            contractId={selectedPayment?.contract_id}
            initialData={selectedPayment}
            mode="register"
            onSubmit={handleFormSubmit}
            onCancel={handleBack}
            title="Регистрация платежа"
            submitText="Зарегистрировать платеж"
          />
        );

      case 'view':
        return (
          <PaymentDetails
            paymentId={selectedPayment?.id || ''}
            onEdit={handlePaymentEdit}
            onBack={handleBack}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="container mx-auto py-6">
      {renderContent()}
    </div>
  );
}
