/**
 * @file: PaymentForm.tsx
 * @description: Форма для создания и регистрации платежа
 * @dependencies: react, react-hook-form, zod, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CreditCard, Save, X, Calendar, DollarSign, FileText, Hash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { usePayment, usePaymentRegistration } from '@pactcrm/supabase-client';
import { Payment, PaymentMethod, RegisterPaymentData } from '@pactcrm/supabase-client';

// Схема валидации для формы платежа
const paymentFormSchema = z.object({
  contract_id: z
    .string()
    .min(1, 'Договор обязателен'),
  amount: z
    .number()
    .min(0.01, 'Сумма должна быть больше 0')
    .max(*********, 'Сумма слишком большая'),
  payment_date: z
    .string()
    .min(1, 'Дата платежа обязательна'),
  payment_method: z
    .enum(['cash', 'bank_transfer', 'card', 'online', 'check', 'other'])
    .refine(val => val !== undefined, 'Метод платежа обязателен'),
  transaction_id: z
    .string()
    .optional(),
  notes: z
    .string()
    .max(500, 'Примечание не должно превышать 500 символов')
    .optional(),
});

type PaymentFormData = z.infer<typeof paymentFormSchema>;

interface PaymentFormProps {
  paymentId?: string;
  contractId?: string;
  initialData?: Partial<Payment>;
  onSubmit?: (data: PaymentFormData) => void | Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  submitText?: string;
  title?: string;
  mode?: 'create' | 'register' | 'edit';
}

/**
 * Компонент формы для создания и регистрации платежа
 */
export function PaymentForm({
  paymentId,
  contractId,
  initialData,
  onSubmit,
  onCancel,
  isLoading: externalLoading = false,
  submitText,
  title,
  mode = 'create',
}: PaymentFormProps) {
  // Хук для работы с платежом
  const {
    payment,
    loading: paymentLoading,
    error: paymentError,
    savePayment,
    isSaving,
    saveError,
  } = usePayment(paymentId);

  // Хук для регистрации платежа
  const {
    registerPayment,
    isRegistering,
    registrationError,
  } = usePaymentRegistration();

  // Определяем режим работы формы
  const isEditMode = Boolean(paymentId);
  const isRegisterMode = mode === 'register';
  const isCreateMode = mode === 'create';

  // Данные для формы
  const formData = initialData || payment;

  // Настройка формы
  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      contract_id: contractId || formData?.contract_id || '',
      amount: formData?.amount || 0,
      payment_date: formData?.payment_date || new Date().toISOString().split('T')[0],
      payment_method: (formData?.payment_method as PaymentMethod) || 'bank_transfer',
      transaction_id: formData?.transaction_id || '',
      notes: '',
    },
  });

  // Обновляем значения формы при изменении данных
  React.useEffect(() => {
    if (formData) {
      form.reset({
        contract_id: contractId || formData.contract_id || '',
        amount: formData.amount || 0,
        payment_date: formData.payment_date || new Date().toISOString().split('T')[0],
        payment_method: (formData.payment_method as PaymentMethod) || 'bank_transfer',
        transaction_id: formData.transaction_id || '',
        notes: '',
      });
    }
  }, [formData, contractId, form]);

  // Обработчик отправки формы
  const handleSubmit = async (data: PaymentFormData) => {
    try {
      if (onSubmit) {
        await onSubmit(data);
      } else if (isRegisterMode) {
        // Регистрация платежа
        const registerData: RegisterPaymentData = {
          contract_id: data.contract_id,
          amount: data.amount,
          payment_date: data.payment_date,
          payment_method: data.payment_method as PaymentMethod,
          transaction_id: data.transaction_id,
          notes: data.notes,
        };
        await registerPayment(registerData);
      } else if (savePayment) {
        // Создание или обновление платежа
        await savePayment({
          contract_id: data.contract_id,
          amount: data.amount,
          due_date: data.payment_date,
          payment_date: isRegisterMode ? data.payment_date : undefined,
          status: isRegisterMode ? 'paid' : 'pending',
          payment_method: data.payment_method,
          transaction_id: data.transaction_id,
        });
      }
    } catch (error) {
      console.error('Ошибка при сохранении платежа:', error);
    }
  };

  const isFormLoading = externalLoading || paymentLoading || isSaving || isRegistering;
  const formError = paymentError || saveError || registrationError;

  // Функция для получения текста метода платежа
  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case 'cash':
        return 'Наличные';
      case 'bank_transfer':
        return 'Банковский перевод';
      case 'card':
        return 'Банковская карта';
      case 'online':
        return 'Онлайн-платеж';
      case 'check':
        return 'Чек';
      case 'other':
        return 'Другое';
      default:
        return method;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <CreditCard className="h-5 w-5" />
          <span>
            {title || (
              isRegisterMode ? 'Регистрация платежа' :
              isEditMode ? 'Редактирование платежа' : 
              'Создание платежа'
            )}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Основная информация */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">Информация о платеже</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Договор */}
                <FormField
                  control={form.control}
                  name="contract_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Договор *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="ID договора"
                          {...field}
                          disabled={isFormLoading || Boolean(contractId)}
                        />
                      </FormControl>
                      <FormDescription>
                        {contractId ? 'Договор выбран автоматически' : 'Укажите ID договора'}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Сумма */}
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center space-x-1">
                        <DollarSign className="h-3 w-3" />
                        <span>Сумма платежа *</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Сумма в рублях
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Дата платежа */}
                <FormField
                  control={form.control}
                  name="payment_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          {isRegisterMode ? 'Дата оплаты *' : 'Срок платежа *'}
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          {...field}
                          disabled={isFormLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        {isRegisterMode ? 'Когда был произведен платеж' : 'Когда должен быть произведен платеж'}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Метод платежа */}
                <FormField
                  control={form.control}
                  name="payment_method"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Метод платежа *</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        disabled={isFormLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Выберите метод платежа" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="cash">{getPaymentMethodText('cash')}</SelectItem>
                          <SelectItem value="bank_transfer">{getPaymentMethodText('bank_transfer')}</SelectItem>
                          <SelectItem value="card">{getPaymentMethodText('card')}</SelectItem>
                          <SelectItem value="online">{getPaymentMethodText('online')}</SelectItem>
                          <SelectItem value="check">{getPaymentMethodText('check')}</SelectItem>
                          <SelectItem value="other">{getPaymentMethodText('other')}</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Дополнительная информация */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">Дополнительная информация</h3>
              </div>
              
              {/* ID транзакции */}
              <FormField
                control={form.control}
                name="transaction_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ID транзакции</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Номер транзакции или чека"
                        {...field}
                        disabled={isFormLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Номер транзакции, чека или другой идентификатор платежа
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Примечания */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Примечания</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Дополнительная информация о платеже..."
                        className="min-h-[80px]"
                        {...field}
                        disabled={isFormLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      Любая дополнительная информация о платеже
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Отображение ошибок */}
            {formError && (
              <div className="p-4 border border-red-200 bg-red-50 rounded-md">
                <p className="text-sm text-red-600">
                  {formError.message || 'Произошла ошибка при сохранении'}
                </p>
              </div>
            )}

            <Separator />

            {/* Кнопки действий */}
            <div className="flex items-center justify-end space-x-2">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isFormLoading}
                >
                  <X className="mr-2 h-4 w-4" />
                  Отмена
                </Button>
              )}
              <Button
                type="submit"
                disabled={isFormLoading}
              >
                {isFormLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    {isRegisterMode ? 'Регистрация...' : 'Сохранение...'}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {submitText || (
                      isRegisterMode ? 'Зарегистрировать платеж' :
                      isEditMode ? 'Сохранить изменения' : 
                      'Создать платеж'
                    )}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
