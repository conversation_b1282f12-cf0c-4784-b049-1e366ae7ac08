/**
 * @file: PaymentsList.tsx
 * @description: Компонент для отображения списка платежей
 * @dependencies: react, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React, { useState, useMemo, useCallback } from 'react';
import { 
  CreditCard, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  MoreHorizontal,
  FileText,
  TrendingUp
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { usePayments } from '@pactcrm/supabase-client';
import { Payment, PaymentStatus, PaymentMethod, PaymentFilters, PaymentPagination, PaymentSorting } from '@pactcrm/supabase-client';

interface PaymentsListProps {
  contractId?: string;
  onPaymentSelect?: (payment: Payment) => void;
  onPaymentCreate?: () => void;
  onPaymentEdit?: (payment: Payment) => void;
  onPaymentDelete?: (payment: Payment) => void;
  onPaymentView?: (payment: Payment) => void;
  onPaymentRegister?: (payment: Payment) => void;
}

/**
 * Компонент для отображения списка платежей
 */
export function PaymentsList({
  contractId,
  onPaymentSelect,
  onPaymentCreate,
  onPaymentEdit,
  onPaymentDelete,
  onPaymentView,
  onPaymentRegister,
}: PaymentsListProps) {
  // Состояние фильтров и пагинации
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<PaymentStatus | 'all'>('all');
  const [methodFilter, setMethodFilter] = useState<PaymentMethod | 'all'>('all');
  const [overdueFilter, setOverdueFilter] = useState<'all' | 'overdue' | 'not_overdue'>('all');
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [sortField, setSortField] = useState<PaymentSorting['field']>('due_date');
  const [sortOrder, setSortOrder] = useState<PaymentSorting['order']>('desc');

  // Подготовка фильтров для API
  const filters = useMemo<PaymentFilters>(() => {
    const result: PaymentFilters = {};
    
    if (search.trim()) {
      result.search = search.trim();
    }
    
    if (contractId) {
      result.contract_id = contractId;
    }
    
    if (statusFilter !== 'all') {
      result.status = statusFilter as PaymentStatus;
    }

    if (methodFilter !== 'all') {
      result.payment_method = methodFilter as PaymentMethod;
    }

    if (overdueFilter === 'overdue') {
      result.is_overdue = true;
    } else if (overdueFilter === 'not_overdue') {
      result.is_overdue = false;
    }
    
    return result;
  }, [search, contractId, statusFilter, methodFilter, overdueFilter]);

  const pagination = useMemo<PaymentPagination>(() => ({
    page,
    limit,
  }), [page, limit]);

  const sorting = useMemo<PaymentSorting>(() => ({
    field: sortField,
    order: sortOrder,
  }), [sortField, sortOrder]);

  // Получение данных
  const {
    payments,
    count,
    total_pages,
    isLoading,
    error,
    refresh,
  } = usePayments(filters, pagination, sorting);

  // Обработчики событий
  const handleSearch = useCallback((value: string) => {
    setSearch(value);
    setPage(1); // Сброс на первую страницу при поиске
  }, []);

  const handleStatusFilter = useCallback((value: string) => {
    setStatusFilter(value as PaymentStatus | 'all');
    setPage(1); // Сброс на первую страницу при фильтрации
  }, []);

  const handleMethodFilter = useCallback((value: string) => {
    setMethodFilter(value as PaymentMethod | 'all');
    setPage(1); // Сброс на первую страницу при фильтрации
  }, []);

  const handleOverdueFilter = useCallback((value: string) => {
    setOverdueFilter(value as 'all' | 'overdue' | 'not_overdue');
    setPage(1); // Сброс на первую страницу при фильтрации
  }, []);

  const handleSort = useCallback((field: PaymentSorting['field']) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('asc');
    }
    setPage(1); // Сброс на первую страницу при сортировке
  }, [sortField, sortOrder]);

  // Функция для получения цвета статуса
  const getStatusColor = (status: string, isOverdue?: boolean) => {
    if (isOverdue) {
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    }
    
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'refunded':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Функция для получения текста статуса
  const getStatusText = (status: string, isOverdue?: boolean) => {
    if (isOverdue) {
      return 'Просрочен';
    }
    
    switch (status) {
      case 'paid':
        return 'Оплачен';
      case 'pending':
        return 'Ожидает оплаты';
      case 'cancelled':
        return 'Отменен';
      case 'refunded':
        return 'Возвращен';
      case 'partial':
        return 'Частично оплачен';
      default:
        return status;
    }
  };

  // Функция для получения иконки статуса
  const getStatusIcon = (status: string, isOverdue?: boolean) => {
    if (isOverdue) {
      return <AlertTriangle className="h-3 w-3" />;
    }
    
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-3 w-3" />;
      case 'pending':
        return <Clock className="h-3 w-3" />;
      case 'cancelled':
        return <XCircle className="h-3 w-3" />;
      case 'refunded':
        return <TrendingUp className="h-3 w-3" />;
      default:
        return <CreditCard className="h-3 w-3" />;
    }
  };

  // Функция для получения текста метода платежа
  const getPaymentMethodText = (method?: string) => {
    switch (method) {
      case 'cash':
        return 'Наличные';
      case 'bank_transfer':
        return 'Банковский перевод';
      case 'card':
        return 'Банковская карта';
      case 'online':
        return 'Онлайн-платеж';
      case 'check':
        return 'Чек';
      default:
        return method || '—';
    }
  };

  // Функция для форматирования суммы
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Функция для форматирования даты
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Ошибка при загрузке платежей</p>
            <Button variant="outline" onClick={refresh} className="mt-2">
              Попробовать снова
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Заголовок и кнопка создания */}
      {!contractId && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CreditCard className="h-6 w-6" />
            <h1 className="text-2xl font-bold">Платежи</h1>
          </div>
          {onPaymentCreate && (
            <Button onClick={onPaymentCreate}>
              <Plus className="mr-2 h-4 w-4" />
              Добавить платеж
            </Button>
          )}
        </div>
      )}

      {/* Фильтры и поиск */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
            {/* Поиск */}
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Поиск по договору, клиенту или номеру транзакции..."
                className="pl-8"
                value={search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>

            {/* Фильтр по статусу */}
            <Select value={statusFilter} onValueChange={handleStatusFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Статус" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все статусы</SelectItem>
                <SelectItem value="paid">Оплачен</SelectItem>
                <SelectItem value="pending">Ожидает оплаты</SelectItem>
                <SelectItem value="cancelled">Отменен</SelectItem>
                <SelectItem value="refunded">Возвращен</SelectItem>
              </SelectContent>
            </Select>

            {/* Фильтр по методу платежа */}
            <Select value={methodFilter} onValueChange={handleMethodFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <CreditCard className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Метод платежа" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все методы</SelectItem>
                <SelectItem value="cash">Наличные</SelectItem>
                <SelectItem value="bank_transfer">Банковский перевод</SelectItem>
                <SelectItem value="card">Банковская карта</SelectItem>
                <SelectItem value="online">Онлайн-платеж</SelectItem>
                <SelectItem value="check">Чек</SelectItem>
              </SelectContent>
            </Select>

            {/* Фильтр по просрочке */}
            <Select value={overdueFilter} onValueChange={handleOverdueFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <AlertTriangle className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Просрочка" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Все платежи</SelectItem>
                <SelectItem value="overdue">Просроченные</SelectItem>
                <SelectItem value="not_overdue">Не просроченные</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Таблица */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                {!contractId && (
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('contract_number')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Договор</span>
                      {sortField === 'contract_number' && (
                        <span className="text-xs">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </TableHead>
                )}
                {!contractId && (
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('client_name')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Клиент</span>
                      {sortField === 'client_name' && (
                        <span className="text-xs">
                          {sortOrder === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </TableHead>
                )}
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50 text-right"
                  onClick={() => handleSort('amount')}
                >
                  <div className="flex items-center justify-end space-x-1">
                    <span>Сумма</span>
                    {sortField === 'amount' && (
                      <span className="text-xs">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('due_date')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Срок платежа</span>
                    {sortField === 'due_date' && (
                      <span className="text-xs">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead>Дата оплаты</TableHead>
                <TableHead>Статус</TableHead>
                <TableHead>Метод платежа</TableHead>
                <TableHead className="w-[50px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Skeleton loading
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={index}>
                    {!contractId && <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>}
                    {!contractId && <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>}
                    <TableCell className="text-right"><Skeleton className="h-4 w-[100px] ml-auto" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                    <TableCell><Skeleton className="h-6 w-[100px]" /></TableCell>
                    <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
                    <TableCell><Skeleton className="h-8 w-8" /></TableCell>
                  </TableRow>
                ))
              ) : payments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={contractId ? 6 : 8} className="text-center py-8">
                    <div className="flex flex-col items-center space-y-2">
                      <CreditCard className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground">
                        {search || statusFilter !== 'all' || methodFilter !== 'all' || overdueFilter !== 'all'
                          ? 'Платежи не найдены' 
                          : 'Пока нет платежей'
                        }
                      </p>
                      {onPaymentCreate && !search && statusFilter === 'all' && methodFilter === 'all' && overdueFilter === 'all' && (
                        <Button variant="outline" onClick={onPaymentCreate}>
                          <Plus className="mr-2 h-4 w-4" />
                          Добавить первый платеж
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                payments.map((payment) => (
                  <TableRow 
                    key={payment.id}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onPaymentSelect?.(payment)}
                  >
                    {!contractId && (
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span>{payment.contract_number}</span>
                        </div>
                      </TableCell>
                    )}
                    {!contractId && (
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <span>{payment.client_name}</span>
                          {payment.apartment_number && (
                            <span className="text-xs text-muted-foreground">
                              кв. {payment.apartment_number}
                            </span>
                          )}
                        </div>
                      </TableCell>
                    )}
                    <TableCell className="text-right font-medium">
                      {formatAmount(payment.amount)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className={payment.is_overdue ? 'text-red-600' : ''}>
                          {formatDate(payment.due_date)}
                        </span>
                        {payment.is_overdue && payment.days_overdue && (
                          <span className="text-xs text-red-600">
                            (+{payment.days_overdue} дн.)
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {payment.payment_date ? (
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3 text-muted-foreground" />
                          <span>{formatDate(payment.payment_date)}</span>
                        </div>
                      ) : (
                        <span className="text-muted-foreground">—</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(payment.status, payment.is_overdue)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(payment.status, payment.is_overdue)}
                          <span>{getStatusText(payment.status, payment.is_overdue)}</span>
                        </div>
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm">
                        {getPaymentMethodText(payment.payment_method)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Открыть меню</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Действия</DropdownMenuLabel>
                          {onPaymentView && (
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              onPaymentView(payment);
                            }}>
                              <Eye className="mr-2 h-4 w-4" />
                              Просмотр
                            </DropdownMenuItem>
                          )}
                          {payment.status === 'pending' && onPaymentRegister && (
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              onPaymentRegister(payment);
                            }}>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Зарегистрировать оплату
                            </DropdownMenuItem>
                          )}
                          {onPaymentEdit && (
                            <DropdownMenuItem onClick={(e) => {
                              e.stopPropagation();
                              onPaymentEdit(payment);
                            }}>
                              <Edit className="mr-2 h-4 w-4" />
                              Редактировать
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          {onPaymentDelete && (
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={(e) => {
                                e.stopPropagation();
                                onPaymentDelete(payment);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Удалить
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Пагинация */}
      {total_pages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Показано {payments.length} из {count} платежей
          </p>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page <= 1}
            >
              Назад
            </Button>
            <span className="text-sm">
              Страница {page} из {total_pages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= total_pages}
            >
              Далее
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
