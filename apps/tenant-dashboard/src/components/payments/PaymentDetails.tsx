/**
 * @file: PaymentDetails.tsx
 * @description: Компонент детального просмотра платежа
 * @dependencies: react, @pactcrm/supabase-client, @pactcrm/ui
 * @created: 2024-12-26
 */

"use client";

import React from 'react';
import { 
  CreditCard, 
  Calendar, 
  DollarSign,
  Edit,
  ArrowLeft,
  FileText,
  User,
  Building,
  Hash,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  TrendingUp,
  Receipt
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { usePayment } from '@pactcrm/supabase-client';
import { Payment } from '@pactcrm/supabase-client';

interface PaymentDetailsProps {
  paymentId: string;
  onEdit?: (payment: Payment) => void;
  onBack?: () => void;
  showActions?: boolean;
}

/**
 * Компонент детального просмотра платежа
 */
export function PaymentDetails({
  paymentId,
  onEdit,
  onBack,
  showActions = true,
}: PaymentDetailsProps) {
  // Получение данных о платеже
  const {
    payment,
    loading: paymentLoading,
    error: paymentError,
  } = usePayment(paymentId);

  // Функция для получения цвета статуса
  const getStatusColor = (status: string, isOverdue?: boolean) => {
    if (isOverdue) {
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    }
    
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'refunded':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  // Функция для получения текста статуса
  const getStatusText = (status: string, isOverdue?: boolean) => {
    if (isOverdue) {
      return 'Просрочен';
    }
    
    switch (status) {
      case 'paid':
        return 'Оплачен';
      case 'pending':
        return 'Ожидает оплаты';
      case 'cancelled':
        return 'Отменен';
      case 'refunded':
        return 'Возвращен';
      case 'partial':
        return 'Частично оплачен';
      default:
        return status;
    }
  };

  // Функция для получения иконки статуса
  const getStatusIcon = (status: string, isOverdue?: boolean) => {
    if (isOverdue) {
      return <AlertTriangle className="h-4 w-4" />;
    }
    
    switch (status) {
      case 'paid':
        return <CheckCircle className="h-4 w-4" />;
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      case 'refunded':
        return <TrendingUp className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  // Функция для получения текста метода платежа
  const getPaymentMethodText = (method?: string) => {
    switch (method) {
      case 'cash':
        return 'Наличные';
      case 'bank_transfer':
        return 'Банковский перевод';
      case 'card':
        return 'Банковская карта';
      case 'online':
        return 'Онлайн-платеж';
      case 'check':
        return 'Чек';
      case 'other':
        return 'Другое';
      default:
        return method || '—';
    }
  };

  // Функция для форматирования суммы
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Функция для форматирования даты
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  // Функция для форматирования даты и времени
  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (paymentError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Ошибка при загрузке платежа</p>
            <p className="text-sm text-muted-foreground mt-1">
              {paymentError.message}
            </p>
            {onBack && (
              <Button variant="outline" onClick={onBack} className="mt-4">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Назад
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (paymentLoading || !payment) {
    return (
      <div className="space-y-6">
        {/* Заголовок */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-[300px]" />
            <Skeleton className="h-4 w-[200px]" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-10 w-[100px]" />
            <Skeleton className="h-10 w-[120px]" />
          </div>
        </div>

        {/* Карточки */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Array.from({ length: 2 }).map((_, index) => (
            <Card key={index}>
              <CardHeader>
                <Skeleton className="h-6 w-[200px]" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-[80%]" />
                <Skeleton className="h-4 w-[60%]" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Заголовок и действия */}
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            {onBack && (
              <Button variant="ghost" size="sm" onClick={onBack}>
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <CreditCard className="h-6 w-6" />
            <h1 className="text-2xl font-bold">
              Платеж {formatAmount(payment.amount)}
            </h1>
            <Badge className={getStatusColor(payment.status, payment.is_overdue)}>
              <div className="flex items-center space-x-1">
                {getStatusIcon(payment.status, payment.is_overdue)}
                <span>{getStatusText(payment.status, payment.is_overdue)}</span>
              </div>
            </Badge>
          </div>
          <div className="flex items-center space-x-4 text-muted-foreground">
            <div className="flex items-center space-x-1">
              <FileText className="h-4 w-4" />
              <span>Договор: {payment.contract_number}</span>
            </div>
            {payment.client_name && (
              <div className="flex items-center space-x-1">
                <User className="h-4 w-4" />
                <span>{payment.client_name}</span>
              </div>
            )}
          </div>
        </div>

        {showActions && (
          <div className="flex space-x-2">
            {onEdit && (
              <Button onClick={() => onEdit(payment)}>
                <Edit className="mr-2 h-4 w-4" />
                Редактировать
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Основная информация */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Информация о платеже */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4" />
              <span>Информация о платеже</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Сумма</Label>
                <p className="text-2xl font-bold">{formatAmount(payment.amount)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Статус</Label>
                <div className="mt-1">
                  <Badge className={getStatusColor(payment.status, payment.is_overdue)}>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(payment.status, payment.is_overdue)}
                      <span>{getStatusText(payment.status, payment.is_overdue)}</span>
                    </div>
                  </Badge>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Срок платежа</Label>
                  <p className={`text-sm ${payment.is_overdue ? 'text-red-600' : ''}`}>
                    {formatDate(payment.due_date)}
                    {payment.is_overdue && payment.days_overdue && (
                      <span className="ml-2 text-red-600">
                        (просрочен на {payment.days_overdue} дн.)
                      </span>
                    )}
                  </p>
                </div>
              </div>

              {payment.payment_date && (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Дата оплаты</Label>
                    <p className="text-sm">{formatDate(payment.payment_date)}</p>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Receipt className="h-4 w-4 text-muted-foreground" />
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Метод платежа</Label>
                  <p className="text-sm">{getPaymentMethodText(payment.payment_method)}</p>
                </div>
              </div>

              {payment.transaction_id && (
                <div className="flex items-center space-x-2">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">ID транзакции</Label>
                    <p className="text-sm font-mono">{payment.transaction_id}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Информация о договоре и объекте */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Договор и объект</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Номер договора</Label>
              <p className="text-lg font-medium">{payment.contract_number}</p>
            </div>

            {payment.client_name && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Клиент</Label>
                <p className="text-sm">{payment.client_name}</p>
              </div>
            )}

            <Separator />

            {payment.complex_name && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <Label className="text-sm font-medium text-muted-foreground">Объект недвижимости</Label>
                </div>
                
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">Комплекс:</span> {payment.complex_name}</p>
                  {payment.building_name && (
                    <p><span className="font-medium">Здание:</span> {payment.building_name}</p>
                  )}
                  {payment.apartment_number && (
                    <p><span className="font-medium">Квартира:</span> {payment.apartment_number}</p>
                  )}
                </div>
              </div>
            )}

            {payment.contract && (
              <div className="space-y-2">
                <Label className="text-sm font-medium text-muted-foreground">Информация о договоре</Label>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">Общая сумма:</span> {formatAmount(payment.contract.total_amount)}</p>
                  <p><span className="font-medium">Ежемесячный платеж:</span> {formatAmount(payment.contract.monthly_payment)}</p>
                  <p><span className="font-medium">Статус договора:</span> {payment.contract.status}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Системная информация */}
      <Card>
        <CardHeader>
          <CardTitle>Системная информация</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Создан</Label>
              <p>{formatDateTime(payment.created_at)}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Обновлен</Label>
              <p>{formatDateTime(payment.updated_at)}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">ID платежа</Label>
              <p className="font-mono">{payment.id}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">ID договора</Label>
              <p className="font-mono">{payment.contract_id}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
