/**
 * @file: auth-signup/index.ts
 * @description: Edge Function для регистрации пользователей с ролями
 * @dependencies: @supabase/supabase-js
 * @created: 2025-01-26
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SignUpRequest {
  email: string
  password: string
  role: string
  tenant_id?: string
  first_name?: string
  last_name?: string
  phone?: string
}

interface SignUpResponse {
  success: boolean
  user?: any
  error?: string
}

serve(async (req) => {
  // Обработка CORS preflight запросов
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Создаем Supabase клиент с service role ключом
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    // Создаем обычный клиент для проверки авторизации
    const authHeader = req.headers.get('Authorization')!
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Проверяем авторизацию пользователя
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Unauthorized' }),
        {
          status: 401,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Проверяем права на создание пользователей
    const userRole = user.user_metadata?.role
    if (!['superadmin', 'support', 'tenant_admin'].includes(userRole)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Insufficient permissions' }),
        {
          status: 403,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Парсим тело запроса
    const requestData: SignUpRequest = await req.json()
    const { email, password, role, tenant_id, first_name, last_name, phone } = requestData

    // Валидация данных
    if (!email || !password || !role) {
      return new Response(
        JSON.stringify({ success: false, error: 'Missing required fields: email, password, role' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Проверяем валидность роли
    const validRoles = ['superadmin', 'support', 'tenant_admin', 'after_sales_manager', 'client']
    if (!validRoles.includes(role)) {
      return new Response(
        JSON.stringify({ success: false, error: 'Invalid role' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Проверяем права на создание пользователей определенных ролей
    if (userRole === 'tenant_admin') {
      // Tenant admin может создавать только after_sales_manager и client
      if (!['after_sales_manager', 'client'].includes(role)) {
        return new Response(
          JSON.stringify({ success: false, error: 'Tenant admin can only create after_sales_manager and client users' }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }

      // Проверяем, что tenant_id соответствует tenant_id создающего пользователя
      const userTenantId = user.user_metadata?.tenant_id
      if (tenant_id !== userTenantId) {
        return new Response(
          JSON.stringify({ success: false, error: 'Cannot create users for different tenant' }),
          {
            status: 403,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          }
        )
      }
    }

    // Для ролей, требующих tenant_id, проверяем его наличие
    if (['tenant_admin', 'after_sales_manager', 'client'].includes(role) && !tenant_id) {
      return new Response(
        JSON.stringify({ success: false, error: 'tenant_id is required for this role' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Создаем пользователя
    const { data: newUser, error: createError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Автоматически подтверждаем email
      user_metadata: {
        role,
        tenant_id,
        first_name,
        last_name,
        phone,
        created_by: user.id,
        created_at: new Date().toISOString(),
      }
    })

    if (createError) {
      console.error('Error creating user:', createError)
      return new Response(
        JSON.stringify({ success: false, error: createError.message }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      )
    }

    // Логируем создание пользователя
    console.log(`User created: ${email} with role ${role} by ${user.email}`)

    const response: SignUpResponse = {
      success: true,
      user: {
        id: newUser.user?.id,
        email: newUser.user?.email,
        role,
        tenant_id,
        first_name,
        last_name,
        phone,
      }
    }

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )

  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    )
  }
})
