/**
 * @file: payments.ts
 * @description: Типы данных для работы с платежами
 * @dependencies: supabase.ts
 * @created: 2024-12-26
 */

import { Database } from './supabase';

/**
 * Тип платежа из базы данных
 */
export type DbPayment = Database['public']['Tables']['payments']['Row'];

/**
 * Тип для создания нового платежа
 */
export type PaymentInsert = Database['public']['Tables']['payments']['Insert'];

/**
 * Тип для обновления платежа
 */
export type PaymentUpdate = Database['public']['Tables']['payments']['Update'];

/**
 * Статусы платежа
 */
export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIAL = 'partial',
}

/**
 * Методы платежа
 */
export enum PaymentMethod {
  CASH = 'cash',
  BANK_TRANSFER = 'bank_transfer',
  CARD = 'card',
  ONLINE = 'online',
  CHECK = 'check',
  OTHER = 'other',
}

/**
 * Типы платежей
 */
export enum PaymentType {
  INITIAL = 'initial',
  MONTHLY = 'monthly',
  PENALTY = 'penalty',
  ADDITIONAL = 'additional',
  REFUND = 'refund',
}

/**
 * Расширенный тип платежа с дополнительными данными
 */
export interface Payment extends DbPayment {
  contract_number?: string;
  client_name?: string;
  client_id?: string;
  apartment_number?: string;
  building_name?: string;
  complex_name?: string;
  is_overdue?: boolean;
  days_overdue?: number;
  payment_type?: PaymentType;
  contract?: Contract;
  client?: Client;
}

/**
 * Интерфейс для графика платежей
 */
export interface PaymentSchedule {
  id: string;
  contract_id: string;
  tenant_id: string;
  payment_number: number;
  amount: number;
  due_date: string;
  payment_type: PaymentType;
  status: PaymentStatus;
  payment_id?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Интерфейс для создания графика платежей
 */
export interface PaymentScheduleInsert {
  contract_id: string;
  tenant_id: string;
  payment_number: number;
  amount: number;
  due_date: string;
  payment_type: PaymentType;
  status?: PaymentStatus;
}

/**
 * Фильтры для платежей
 */
export interface PaymentFilters {
  search?: string;
  contract_id?: string;
  client_id?: string;
  status?: PaymentStatus | PaymentStatus[];
  payment_method?: PaymentMethod | PaymentMethod[];
  payment_type?: PaymentType | PaymentType[];
  due_date_from?: string;
  due_date_to?: string;
  payment_date_from?: string;
  payment_date_to?: string;
  amount_min?: number;
  amount_max?: number;
  is_overdue?: boolean;
  complex_id?: string;
  building_id?: string;
}

/**
 * Параметры пагинации для платежей
 */
export interface PaymentPagination {
  page?: number;
  limit?: number;
  offset?: number;
}

/**
 * Параметры сортировки для платежей
 */
export interface PaymentSorting {
  field?: 'due_date' | 'payment_date' | 'amount' | 'status' | 'created_at' | 'updated_at' | 'contract_number' | 'client_name';
  order?: 'asc' | 'desc';
}

/**
 * Результат запроса платежей
 */
export interface PaymentsResult {
  data: Payment[];
  count: number;
  page: number;
  limit: number;
  total_pages: number;
}

/**
 * Статистика по платежам
 */
export interface PaymentStats {
  total_payments: number;
  paid_payments: number;
  pending_payments: number;
  overdue_payments: number;
  cancelled_payments: number;
  total_amount: number;
  paid_amount: number;
  pending_amount: number;
  overdue_amount: number;
  average_payment_amount: number;
  on_time_payment_rate: number;
  overdue_payment_rate: number;
}

/**
 * Статистика по платежам за период
 */
export interface PaymentPeriodStats {
  period: string;
  total_amount: number;
  paid_amount: number;
  pending_amount: number;
  overdue_amount: number;
  payments_count: number;
  paid_count: number;
  pending_count: number;
  overdue_count: number;
}

/**
 * Статистика по договору
 */
export interface ContractPaymentStats {
  contract_id: string;
  total_scheduled: number;
  total_paid: number;
  total_pending: number;
  total_overdue: number;
  scheduled_amount: number;
  paid_amount: number;
  pending_amount: number;
  overdue_amount: number;
  completion_percentage: number;
  next_payment_date?: string;
  next_payment_amount?: number;
  last_payment_date?: string;
  last_payment_amount?: number;
}

/**
 * Данные для создания графика платежей
 */
export interface CreatePaymentScheduleData {
  contract_id: string;
  total_amount: number;
  initial_payment: number;
  monthly_payment: number;
  start_date: string;
  end_date: string;
  payment_day: number; // День месяца для платежа (1-31)
}

/**
 * Результат создания графика платежей
 */
export interface CreatePaymentScheduleResult {
  schedule: PaymentSchedule[];
  total_payments: number;
  total_amount: number;
  monthly_amount: number;
}

/**
 * Данные для регистрации платежа
 */
export interface RegisterPaymentData {
  schedule_id?: string;
  contract_id: string;
  amount: number;
  payment_date: string;
  payment_method: PaymentMethod;
  transaction_id?: string;
  notes?: string;
}

/**
 * Результат регистрации платежа
 */
export interface RegisterPaymentResult {
  payment: Payment;
  updated_schedule?: PaymentSchedule;
  remaining_amount?: number;
}

/**
 * Данные для массового обновления платежей
 */
export interface BulkPaymentUpdate {
  payment_ids: string[];
  updates: Partial<PaymentUpdate>;
}

/**
 * Результат массового обновления платежей
 */
export interface BulkPaymentUpdateResult {
  updated: Payment[];
  errors: Array<{
    payment_id: string;
    error: string;
  }>;
}

/**
 * Отчет по платежам
 */
export interface PaymentReport {
  period: {
    start_date: string;
    end_date: string;
  };
  summary: PaymentStats;
  by_status: Array<{
    status: PaymentStatus;
    count: number;
    amount: number;
    percentage: number;
  }>;
  by_method: Array<{
    method: PaymentMethod;
    count: number;
    amount: number;
    percentage: number;
  }>;
  by_period: PaymentPeriodStats[];
  overdue_analysis: {
    total_overdue: number;
    average_days_overdue: number;
    overdue_by_ranges: Array<{
      range: string;
      count: number;
      amount: number;
    }>;
  };
}

/**
 * Параметры для генерации отчета
 */
export interface PaymentReportParams {
  start_date: string;
  end_date: string;
  contract_ids?: string[];
  client_ids?: string[];
  complex_ids?: string[];
  include_overdue_analysis?: boolean;
  group_by_period?: 'day' | 'week' | 'month' | 'quarter';
}

/**
 * Уведомление о платеже
 */
export interface PaymentNotification {
  id: string;
  payment_id: string;
  contract_id: string;
  client_id: string;
  tenant_id: string;
  type: 'reminder' | 'overdue' | 'received' | 'failed';
  message: string;
  sent_at?: string;
  delivery_status: 'pending' | 'sent' | 'delivered' | 'failed';
  delivery_method: 'email' | 'sms' | 'whatsapp' | 'push';
  created_at: string;
}

/**
 * Данные для отправки уведомления
 */
export interface SendPaymentNotificationData {
  payment_id: string;
  type: 'reminder' | 'overdue' | 'received' | 'failed';
  delivery_method: 'email' | 'sms' | 'whatsapp' | 'push';
  message?: string;
  schedule_at?: string;
}

// Импорт типов для избежания циклических зависимостей
interface Contract {
  id: string;
  client_id: string;
  apartment_id: string;
  number: string;
  total_amount: number;
  monthly_payment: number;
  status: string;
}

interface Client {
  id: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  email?: string;
  phone?: string;
}
