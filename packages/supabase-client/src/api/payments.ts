/**
 * @file: payments.ts
 * @description: API функции для работы с платежами
 * @dependencies: @supabase/supabase-js, types/payments
 * @created: 2024-12-26
 */

import { SupabaseClient } from '@supabase/supabase-js';
import {
  Payment,
  PaymentInsert,
  PaymentUpdate,
  PaymentFilters,
  PaymentPagination,
  PaymentSorting,
  PaymentsResult,
  PaymentStats,
  PaymentSchedule,
  PaymentScheduleInsert,
  CreatePaymentScheduleData,
  CreatePaymentScheduleResult,
  RegisterPaymentData,
  RegisterPaymentResult,
  ContractPaymentStats,
  PaymentReport,
  PaymentReportParams,
  BulkPaymentUpdate,
  BulkPaymentUpdateResult,
  PaymentStatus,
  PaymentType,
} from '../types/payments';

/**
 * Получение списка платежей с фильтрацией, пагинацией и сортировкой
 *
 * @param supabase Клиент Supabase
 * @param filters Фильтры для платежей
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Результат запроса с платежами и метаданными
 */
export async function getPayments(
  supabase: SupabaseClient,
  filters?: PaymentFilters,
  pagination?: PaymentPagination,
  sorting?: PaymentSorting
): Promise<PaymentsResult> {
  try {
    // Базовый запрос с джойнами для получения дополнительной информации
    let query = supabase
      .from('payments')
      .select(`
        *,
        contracts!payments_contract_id_fkey (
          id,
          number,
          client_id,
          apartment_id,
          total_amount,
          monthly_payment,
          status,
          clients!contracts_client_id_fkey (
            id,
            first_name,
            last_name,
            middle_name
          ),
          apartments!contracts_apartment_id_fkey (
            id,
            number,
            floor,
            buildings!apartments_building_id_fkey (
              id,
              name,
              complexes!buildings_complex_id_fkey (
                id,
                name
              )
            )
          )
        )
      `, { count: 'exact' });

    // Применение фильтров
    if (filters) {
      if (filters.search) {
        // Поиск по номеру договора, имени клиента или номеру квартиры
        query = query.or(`
          contracts.number.ilike.%${filters.search}%,
          contracts.clients.first_name.ilike.%${filters.search}%,
          contracts.clients.last_name.ilike.%${filters.search}%,
          contracts.apartments.number.ilike.%${filters.search}%,
          transaction_id.ilike.%${filters.search}%
        `);
      }

      if (filters.contract_id) {
        query = query.eq('contract_id', filters.contract_id);
      }

      if (filters.status) {
        const statusArray = Array.isArray(filters.status) ? filters.status : [filters.status];
        query = query.in('status', statusArray);
      }

      if (filters.payment_method) {
        const methodArray = Array.isArray(filters.payment_method) ? filters.payment_method : [filters.payment_method];
        query = query.in('payment_method', methodArray);
      }

      if (filters.due_date_from) {
        query = query.gte('due_date', filters.due_date_from);
      }

      if (filters.due_date_to) {
        query = query.lte('due_date', filters.due_date_to);
      }

      if (filters.payment_date_from) {
        query = query.gte('payment_date', filters.payment_date_from);
      }

      if (filters.payment_date_to) {
        query = query.lte('payment_date', filters.payment_date_to);
      }

      if (filters.amount_min !== undefined) {
        query = query.gte('amount', filters.amount_min);
      }

      if (filters.amount_max !== undefined) {
        query = query.lte('amount', filters.amount_max);
      }
    }

    // Применение сортировки
    if (sorting?.field && sorting?.order) {
      query = query.order(sorting.field, { ascending: sorting.order === 'asc' });
    } else {
      // Сортировка по умолчанию - по дате платежа (сначала новые)
      query = query.order('due_date', { ascending: false });
    }

    // Применение пагинации
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    const offset = pagination?.offset || (page - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    if (!data) {
      return {
        data: [],
        count: 0,
        page,
        limit,
        total_pages: 0,
      };
    }

    // Обработка данных и добавление вычисляемых полей
    const payments: Payment[] = data.map((payment) => {
      const contract = payment.contracts;
      const client = contract?.clients;
      const apartment = contract?.apartments;
      const building = apartment?.buildings;
      const complex = building?.complexes;

      // Проверяем просрочку
      const dueDate = new Date(payment.due_date);
      const today = new Date();
      const isOverdue = payment.status === 'pending' && dueDate < today;
      const daysOverdue = isOverdue ? Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;

      // Определяем тип платежа на основе суммы и позиции
      let paymentType: PaymentType = PaymentType.MONTHLY;
      if (payment.amount === contract?.total_amount) {
        paymentType = PaymentType.INITIAL;
      } else if (payment.amount !== contract?.monthly_payment) {
        paymentType = PaymentType.ADDITIONAL;
      }

      // Удаляем вложенные объекты для чистоты ответа
      const { contracts: _, ...paymentData } = payment;

      return {
        ...paymentData,
        contract_number: contract?.number,
        client_name: client ? `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim() : undefined,
        client_id: client?.id,
        apartment_number: apartment?.number,
        building_name: building?.name,
        complex_name: complex?.name,
        is_overdue: isOverdue,
        days_overdue: daysOverdue,
        payment_type: paymentType,
      };
    });

    // Применение дополнительных фильтров после обработки данных
    let filteredPayments = payments;

    if (filters?.is_overdue !== undefined) {
      filteredPayments = filteredPayments.filter(p => p.is_overdue === filters.is_overdue);
    }

    const total_pages = Math.ceil((count || 0) / limit);

    return {
      data: filteredPayments,
      count: count || 0,
      page,
      limit,
      total_pages,
    };
  } catch (error) {
    console.error('Ошибка при получении платежей:', error);
    throw error;
  }
}

/**
 * Получение платежа по ID
 *
 * @param supabase Клиент Supabase
 * @param id ID платежа
 * @returns Платеж с дополнительной информацией
 */
export async function getPaymentById(
  supabase: SupabaseClient,
  id: string
): Promise<Payment | null> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        contracts!payments_contract_id_fkey (
          id,
          number,
          signed_date,
          total_amount,
          initial_payment,
          monthly_payment,
          status,
          clients!contracts_client_id_fkey (
            id,
            first_name,
            last_name,
            middle_name,
            email,
            phone
          ),
          apartments!contracts_apartment_id_fkey (
            id,
            number,
            floor,
            rooms,
            area,
            buildings!apartments_building_id_fkey (
              id,
              name,
              complexes!buildings_complex_id_fkey (
                id,
                name
              )
            )
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      return null;
    }

    const contract = data.contracts;
    const client = contract?.clients;
    const apartment = contract?.apartments;
    const building = apartment?.buildings;
    const complex = building?.complexes;

    // Проверяем просрочку
    const dueDate = new Date(data.due_date);
    const today = new Date();
    const isOverdue = data.status === 'pending' && dueDate < today;
    const daysOverdue = isOverdue ? Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;

    const payment: Payment = {
      ...data,
      contract_number: contract?.number,
      client_name: client ? `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim() : undefined,
      client_id: client?.id,
      apartment_number: apartment?.number,
      building_name: building?.name,
      complex_name: complex?.name,
      is_overdue: isOverdue,
      days_overdue: daysOverdue,
      contract: contract ? {
        ...contract,
        client_name: client ? `${client.last_name} ${client.first_name} ${client.middle_name || ''}`.trim() : undefined,
        apartment_info: apartment ? `${complex?.name} - ${building?.name}, кв. ${apartment.number}` : undefined,
      } : undefined,
    };

    return payment;
  } catch (error) {
    console.error('Ошибка при получении платежа:', error);
    throw error;
  }
}

/**
 * Создание нового платежа
 *
 * @param supabase Клиент Supabase
 * @param paymentData Данные для создания платежа
 * @returns Созданный платеж
 */
export async function createPayment(
  supabase: SupabaseClient,
  paymentData: PaymentInsert
): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .insert(paymentData)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось создать платеж');
    }

    // Получаем полную информацию о платеже
    return await getPaymentById(supabase, data.id) || data;
  } catch (error) {
    console.error('Ошибка при создании платежа:', error);
    throw error;
  }
}

/**
 * Обновление платежа
 *
 * @param supabase Клиент Supabase
 * @param id ID платежа
 * @param updates Данные для обновления
 * @returns Обновленный платеж
 */
export async function updatePayment(
  supabase: SupabaseClient,
  id: string,
  updates: PaymentUpdate
): Promise<Payment> {
  try {
    const { data, error } = await supabase
      .from('payments')
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error('Не удалось обновить платеж');
    }

    // Получаем полную информацию о платеже
    return await getPaymentById(supabase, id) || data;
  } catch (error) {
    console.error('Ошибка при обновлении платежа:', error);
    throw error;
  }
}

/**
 * Удаление платежа
 *
 * @param supabase Клиент Supabase
 * @param id ID платежа
 * @returns Результат удаления
 */
export async function deletePayment(
  supabase: SupabaseClient,
  id: string
): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('payments')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Ошибка при удалении платежа:', error);
    throw error;
  }
}

/**
 * Получение статистики по платежам
 *
 * @param supabase Клиент Supabase
 * @param tenantId ID арендатора
 * @param contractId ID договора (опционально)
 * @returns Статистика по платежам
 */
export async function getPaymentStats(
  supabase: SupabaseClient,
  tenantId: string,
  contractId?: string
): Promise<PaymentStats> {
  try {
    let query = supabase
      .from('payments')
      .select('*')
      .eq('tenant_id', tenantId);

    if (contractId) {
      query = query.eq('contract_id', contractId);
    }

    const { data: payments, error } = await query;

    if (error) {
      throw error;
    }

    if (!payments) {
      return {
        total_payments: 0,
        paid_payments: 0,
        pending_payments: 0,
        overdue_payments: 0,
        cancelled_payments: 0,
        total_amount: 0,
        paid_amount: 0,
        pending_amount: 0,
        overdue_amount: 0,
        average_payment_amount: 0,
        on_time_payment_rate: 0,
        overdue_payment_rate: 0,
      };
    }

    const today = new Date();

    const total_payments = payments.length;
    const paid_payments = payments.filter(p => p.status === 'paid').length;
    const pending_payments = payments.filter(p => p.status === 'pending').length;
    const cancelled_payments = payments.filter(p => p.status === 'cancelled').length;

    // Подсчет просроченных платежей
    const overdue_payments = payments.filter(p =>
      p.status === 'pending' && new Date(p.due_date) < today
    ).length;

    const total_amount = payments.reduce((sum, p) => sum + p.amount, 0);
    const paid_amount = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);
    const pending_amount = payments.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0);
    const overdue_amount = payments.filter(p =>
      p.status === 'pending' && new Date(p.due_date) < today
    ).reduce((sum, p) => sum + p.amount, 0);

    const average_payment_amount = total_payments > 0 ? total_amount / total_payments : 0;
    const on_time_payment_rate = total_payments > 0 ? (paid_payments / total_payments) * 100 : 0;
    const overdue_payment_rate = total_payments > 0 ? (overdue_payments / total_payments) * 100 : 0;

    return {
      total_payments,
      paid_payments,
      pending_payments,
      overdue_payments,
      cancelled_payments,
      total_amount,
      paid_amount,
      pending_amount,
      overdue_amount,
      average_payment_amount,
      on_time_payment_rate,
      overdue_payment_rate,
    };
  } catch (error) {
    console.error('Ошибка при получении статистики платежей:', error);
    throw error;
  }
}

/**
 * Получение статистики по платежам для договора
 *
 * @param supabase Клиент Supabase
 * @param contractId ID договора
 * @returns Статистика по платежам договора
 */
export async function getContractPaymentStats(
  supabase: SupabaseClient,
  contractId: string
): Promise<ContractPaymentStats> {
  try {
    const { data: payments, error } = await supabase
      .from('payments')
      .select('*')
      .eq('contract_id', contractId)
      .order('due_date', { ascending: true });

    if (error) {
      throw error;
    }

    if (!payments || payments.length === 0) {
      return {
        contract_id: contractId,
        total_scheduled: 0,
        total_paid: 0,
        total_pending: 0,
        total_overdue: 0,
        scheduled_amount: 0,
        paid_amount: 0,
        pending_amount: 0,
        overdue_amount: 0,
        completion_percentage: 0,
      };
    }

    const today = new Date();

    const total_scheduled = payments.length;
    const total_paid = payments.filter(p => p.status === 'paid').length;
    const total_pending = payments.filter(p => p.status === 'pending').length;
    const total_overdue = payments.filter(p =>
      p.status === 'pending' && new Date(p.due_date) < today
    ).length;

    const scheduled_amount = payments.reduce((sum, p) => sum + p.amount, 0);
    const paid_amount = payments.filter(p => p.status === 'paid').reduce((sum, p) => sum + p.amount, 0);
    const pending_amount = payments.filter(p => p.status === 'pending').reduce((sum, p) => sum + p.amount, 0);
    const overdue_amount = payments.filter(p =>
      p.status === 'pending' && new Date(p.due_date) < today
    ).reduce((sum, p) => sum + p.amount, 0);

    const completion_percentage = scheduled_amount > 0 ? (paid_amount / scheduled_amount) * 100 : 0;

    // Найти следующий платеж
    const nextPayment = payments.find(p => p.status === 'pending' && new Date(p.due_date) >= today);
    const lastPaidPayment = payments.filter(p => p.status === 'paid').pop();

    return {
      contract_id: contractId,
      total_scheduled,
      total_paid,
      total_pending,
      total_overdue,
      scheduled_amount,
      paid_amount,
      pending_amount,
      overdue_amount,
      completion_percentage,
      next_payment_date: nextPayment?.due_date,
      next_payment_amount: nextPayment?.amount,
      last_payment_date: lastPaidPayment?.payment_date || undefined,
      last_payment_amount: lastPaidPayment?.amount,
    };
  } catch (error) {
    console.error('Ошибка при получении статистики платежей по договору:', error);
    throw error;
  }
}

/**
 * Регистрация платежа
 *
 * @param supabase Клиент Supabase
 * @param paymentData Данные для регистрации платежа
 * @returns Результат регистрации платежа
 */
export async function registerPayment(
  supabase: SupabaseClient,
  paymentData: RegisterPaymentData
): Promise<RegisterPaymentResult> {
  try {
    // Создаем новый платеж или обновляем существующий
    const paymentInsert: PaymentInsert = {
      tenant_id: '', // Будет установлен из контекста
      contract_id: paymentData.contract_id,
      amount: paymentData.amount,
      due_date: paymentData.payment_date, // Для зарегистрированного платежа due_date = payment_date
      payment_date: paymentData.payment_date,
      status: 'paid',
      payment_method: paymentData.payment_method,
      transaction_id: paymentData.transaction_id,
    };

    const payment = await createPayment(supabase, paymentInsert);

    return {
      payment,
      remaining_amount: 0, // TODO: Рассчитать остаток по договору
    };
  } catch (error) {
    console.error('Ошибка при регистрации платежа:', error);
    throw error;
  }
}

/**
 * Массовое обновление платежей
 *
 * @param supabase Клиент Supabase
 * @param bulkUpdate Данные для массового обновления
 * @returns Результат массового обновления
 */
export async function bulkUpdatePayments(
  supabase: SupabaseClient,
  bulkUpdate: BulkPaymentUpdate
): Promise<BulkPaymentUpdateResult> {
  try {
    const results = await Promise.allSettled(
      bulkUpdate.payment_ids.map(id => updatePayment(supabase, id, bulkUpdate.updates))
    );

    const updated: Payment[] = [];
    const errors: Array<{ payment_id: string; error: string }> = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        updated.push(result.value);
      } else {
        errors.push({
          payment_id: bulkUpdate.payment_ids[index],
          error: result.reason?.message || 'Неизвестная ошибка',
        });
      }
    });

    return { updated, errors };
  } catch (error) {
    console.error('Ошибка при массовом обновлении платежей:', error);
    throw error;
  }
}

/**
 * Получение просроченных платежей
 *
 * @param supabase Клиент Supabase
 * @param tenantId ID арендатора
 * @param daysOverdue Количество дней просрочки (опционально)
 * @returns Список просроченных платежей
 */
export async function getOverduePayments(
  supabase: SupabaseClient,
  tenantId: string,
  daysOverdue?: number
): Promise<Payment[]> {
  try {
    const today = new Date();
    let cutoffDate = today;

    if (daysOverdue) {
      cutoffDate = new Date(today.getTime() - (daysOverdue * 24 * 60 * 60 * 1000));
    }

    const filters: PaymentFilters = {
      status: PaymentStatus.PENDING,
      due_date_to: cutoffDate.toISOString().split('T')[0],
    };

    const result = await getPayments(supabase, filters, { limit: 1000 });
    return result.data.filter(p => p.is_overdue);
  } catch (error) {
    console.error('Ошибка при получении просроченных платежей:', error);
    throw error;
  }
}
