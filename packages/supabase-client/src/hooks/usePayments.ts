/**
 * @file: usePayments.ts
 * @description: Хуки для работы с платежами
 * @dependencies: react, @supabase/supabase-js, swr
 * @created: 2024-12-26
 */

import { useState, useCallback, useEffect } from 'react';
import { useSupabaseClient } from '../context/SupabaseProvider';
import { useTenant } from '../context/TenantContext';
import useSWR from 'swr';
import {
  Payment,
  PaymentInsert,
  PaymentUpdate,
  PaymentFilters,
  PaymentPagination,
  PaymentSorting,
  PaymentsResult,
  PaymentStats,
  ContractPaymentStats,
  RegisterPaymentData,
  RegisterPaymentResult,
} from '../types/payments';
import {
  getPayments,
  getPaymentById,
  createPayment,
  updatePayment,
  deletePayment,
  getPaymentStats,
  getContractPaymentStats,
  registerPayment,
  bulkUpdatePayments,
  getOverduePayments,
} from '../api/payments';

/**
 * Хук для работы со списком платежей
 * 
 * @param filters Фильтры для платежей
 * @param pagination Параметры пагинации
 * @param sorting Параметры сортировки
 * @returns Объект с данными и функциями для работы с платежами
 */
export function usePayments(
  filters?: PaymentFilters,
  pagination?: PaymentPagination,
  sorting?: PaymentSorting
) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  // Создаем ключ для SWR на основе параметров
  const swrKey = tenantId 
    ? ['payments', tenantId, filters, pagination, sorting]
    : null;

  const {
    data,
    error,
    isLoading,
    mutate,
  } = useSWR<PaymentsResult>(
    swrKey,
    () => getPayments(supabase, filters, pagination, sorting),
    {
      revalidateOnFocus: false,
      dedupingInterval: 30000, // 30 секунд
    }
  );

  const [isCreating, setIsCreating] = useState(false);
  const [createError, setCreateError] = useState<Error | null>(null);

  // Функция для создания платежа
  const createPaymentMutation = useCallback(async (paymentData: Omit<PaymentInsert, 'tenant_id'>) => {
    if (!tenantId) throw new Error('Tenant ID не найден');
    
    try {
      setIsCreating(true);
      setCreateError(null);
      
      const newPayment = await createPayment(supabase, {
        ...paymentData,
        tenant_id: tenantId,
      });
      
      // Обновляем кэш
      await mutate();
      
      return newPayment;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setCreateError(error);
      throw error;
    } finally {
      setIsCreating(false);
    }
  }, [supabase, tenantId, mutate]);

  return {
    payments: data?.data || [],
    count: data?.count || 0,
    page: data?.page || 1,
    limit: data?.limit || 20,
    total_pages: data?.total_pages || 0,
    isLoading,
    error,
    refresh: mutate,
    createPayment: createPaymentMutation,
    isCreating,
    createError,
  };
}

/**
 * Хук для работы с отдельным платежом
 * 
 * @param id ID платежа (если не указан, то создается новый платеж)
 * @returns Объект с данными и функциями для работы с платежом
 */
export function usePayment(id?: string) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();
  
  const [payment, setPayment] = useState<Payment | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<Error | null>(null);

  // Функция для загрузки платежа
  const fetchPayment = useCallback(async () => {
    if (!id || !tenantId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const payment = await getPaymentById(supabase, id);
      setPayment(payment);
    } catch (err) {
      console.error('Ошибка при загрузке платежа:', err);
      setError(err instanceof Error ? err : new Error(String(err)));
    } finally {
      setLoading(false);
    }
  }, [supabase, id, tenantId]);

  // Загружаем платеж при изменении ID
  useEffect(() => {
    if (id) {
      fetchPayment();
    } else {
      setPayment(null);
    }
  }, [id, fetchPayment]);

  // Функция для сохранения платежа (создание или обновление)
  const savePayment = useCallback(async (data: PaymentInsert | PaymentUpdate) => {
    if (!tenantId) return null;
    
    try {
      setIsSaving(true);
      setSaveError(null);
      
      let savedPayment: Payment;
      
      if (id) {
        // Обновление существующего платежа
        savedPayment = await updatePayment(supabase, id, data as PaymentUpdate);
      } else {
        // Создание нового платежа
        savedPayment = await createPayment(supabase, {
          ...data as PaymentInsert,
          tenant_id: tenantId,
        });
      }
      
      setPayment(savedPayment);
      return savedPayment;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setSaveError(error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [supabase, id, tenantId]);

  // Функция для удаления платежа
  const deletePaymentMutation = useCallback(async () => {
    if (!id) return false;
    
    try {
      const result = await deletePayment(supabase, id);
      if (result) {
        setPayment(null);
      }
      return result;
    } catch (err) {
      console.error('Ошибка при удалении платежа:', err);
      throw err;
    }
  }, [supabase, id]);

  return {
    payment,
    loading,
    error,
    isSaving,
    saveError,
    savePayment,
    deletePayment: deletePaymentMutation,
    refresh: fetchPayment,
  };
}

/**
 * Хук для получения статистики по платежам
 * 
 * @param contractId ID договора (опционально)
 * @returns Статистика по платежам
 */
export function usePaymentStats(contractId?: string) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  const swrKey = tenantId ? ['payment-stats', tenantId, contractId] : null;

  const {
    data: stats,
    error,
    isLoading,
    mutate,
  } = useSWR<PaymentStats>(
    swrKey,
    () => tenantId ? getPaymentStats(supabase, tenantId, contractId) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 минута
    }
  );

  return {
    stats,
    isLoading,
    error,
    refresh: mutate,
  };
}

/**
 * Хук для получения статистики по платежам договора
 * 
 * @param contractId ID договора
 * @returns Статистика по платежам договора
 */
export function useContractPaymentStats(contractId?: string) {
  const supabase = useSupabaseClient();

  const swrKey = contractId ? ['contract-payment-stats', contractId] : null;

  const {
    data: stats,
    error,
    isLoading,
    mutate,
  } = useSWR<ContractPaymentStats>(
    swrKey,
    () => contractId ? getContractPaymentStats(supabase, contractId) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1 минута
    }
  );

  return {
    stats,
    isLoading,
    error,
    refresh: mutate,
  };
}

/**
 * Хук для поиска платежей
 * 
 * @param searchQuery Поисковый запрос
 * @param limit Лимит результатов
 * @returns Результаты поиска платежей
 */
export function usePaymentSearch(searchQuery?: string, limit: number = 10) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  const [results, setResults] = useState<Payment[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<Error | null>(null);

  const search = useCallback(async (query: string) => {
    if (!query.trim() || !tenantId) {
      setResults([]);
      return;
    }

    try {
      setIsSearching(true);
      setSearchError(null);

      const filters: PaymentFilters = {
        search: query,
      };

      const pagination: PaymentPagination = {
        page: 1,
        limit,
      };

      const result = await getPayments(supabase, filters, pagination);
      setResults(result.data);
    } catch (err) {
      console.error('Ошибка при поиске платежей:', err);
      setSearchError(err instanceof Error ? err : new Error(String(err)));
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [supabase, tenantId, limit]);

  // Автоматический поиск при изменении запроса
  useEffect(() => {
    if (searchQuery) {
      search(searchQuery);
    } else {
      setResults([]);
    }
  }, [searchQuery, search]);

  const clearResults = useCallback(() => {
    setResults([]);
    setSearchError(null);
  }, []);

  return {
    results,
    isSearching,
    searchError,
    search,
    clearResults,
  };
}

/**
 * Хук для регистрации платежей
 * 
 * @returns Функции для регистрации платежей
 */
export function usePaymentRegistration() {
  const supabase = useSupabaseClient();
  const [isRegistering, setIsRegistering] = useState(false);
  const [registrationError, setRegistrationError] = useState<Error | null>(null);

  const registerPaymentMutation = useCallback(async (paymentData: RegisterPaymentData) => {
    try {
      setIsRegistering(true);
      setRegistrationError(null);

      const result = await registerPayment(supabase, paymentData);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setRegistrationError(error);
      throw error;
    } finally {
      setIsRegistering(false);
    }
  }, [supabase]);

  return {
    registerPayment: registerPaymentMutation,
    isRegistering,
    registrationError,
  };
}

/**
 * Хук для получения просроченных платежей
 * 
 * @param daysOverdue Количество дней просрочки
 * @returns Просроченные платежи
 */
export function useOverduePayments(daysOverdue?: number) {
  const supabase = useSupabaseClient();
  const { tenantId } = useTenant();

  const swrKey = tenantId ? ['overdue-payments', tenantId, daysOverdue] : null;

  const {
    data: overduePayments,
    error,
    isLoading,
    mutate,
  } = useSWR<Payment[]>(
    swrKey,
    () => tenantId ? getOverduePayments(supabase, tenantId, daysOverdue) : null,
    {
      revalidateOnFocus: false,
      dedupingInterval: 300000, // 5 минут
    }
  );

  return {
    overduePayments: overduePayments || [],
    isLoading,
    error,
    refresh: mutate,
  };
}

/**
 * Хук для массовых операций с платежами
 * 
 * @returns Функции для массовых операций с платежами
 */
export function useBulkPaymentOperations() {
  const supabase = useSupabaseClient();
  const [isProcessing, setIsProcessing] = useState(false);
  const [processError, setProcessError] = useState<Error | null>(null);

  const bulkUpdate = useCallback(async (paymentIds: string[], updates: Partial<PaymentUpdate>) => {
    try {
      setIsProcessing(true);
      setProcessError(null);

      const result = await bulkUpdatePayments(supabase, { payment_ids: paymentIds, updates });
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setProcessError(error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [supabase]);

  const bulkDelete = useCallback(async (paymentIds: string[]) => {
    try {
      setIsProcessing(true);
      setProcessError(null);

      const results = await Promise.allSettled(
        paymentIds.map(id => deletePayment(supabase, id))
      );

      const deleted: string[] = [];
      const errors: Array<{ payment_id: string; error: string }> = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          deleted.push(paymentIds[index]);
        } else {
          errors.push({
            payment_id: paymentIds[index],
            error: result.reason?.message || 'Неизвестная ошибка',
          });
        }
      });

      return { deleted, errors };
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      setProcessError(error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  }, [supabase]);

  return {
    bulkUpdate,
    bulkDelete,
    isProcessing,
    processError,
  };
}
