"use client";

import { useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import { createBrowserSupabaseClient, getUserRole, getUserTenantId, getRedirectUrl } from '../auth/auth-config';
import { authService, userUtils } from '../auth/auth-helpers';

/**
 * Хук для управления аутентификацией пользователя
 *
 * Предоставляет доступ к текущему пользователю, сессии и методам аутентификации
 *
 * @returns Объект с данными пользователя, сессии и методами аутентификации
 *
 * @example
 * ```tsx
 * const { user, signIn, signOut } = useAuth();
 *
 * // Проверка авторизации
 * if (user) {
 *   console.log('Пользователь авторизован:', user.email);
 * }
 *
 * // Вход в систему
 * const handleLogin = async () => {
 *   const { error } = await signIn('<EMAIL>', 'password');
 *   if (error) console.error('Ошибка входа:', error.message);
 * };
 * ```
 */
export function useAuth() {
  const supabase = createBrowserSupabaseClient();
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Получаем текущую сессию при инициализации
    const getSession = async () => {
      setLoading(true);

      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          throw error;
        }

        setSession(session);
        setUser(session?.user ?? null);
      } catch (error) {
        console.error('Ошибка при получении сессии:', error);
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    getSession();

    // Подписываемся на изменения аутентификации
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, newSession) => {
        setSession(newSession);
        setUser(newSession?.user ?? null);
        setLoading(false);
      }
    );

    // Отписываемся при размонтировании компонента
    return () => {
      subscription.unsubscribe();
    };
  }, [supabase]);

  /**
   * Вход пользователя по email и паролю
   *
   * @param email Email пользователя
   * @param password Пароль пользователя
   * @returns Результат операции входа
   */
  const signIn = async (email: string, password: string) => {
    setError(null);
    setLoading(true);

    try {
      const result = await authService.signIn({ email, password });

      if (result.error) {
        setError(result.error.message);
        return result;
      }

      // Проверяем роль пользователя и перенаправляем
      if (result.user) {
        const userRole = getUserRole({ user: result.user });
        const redirectUrl = getRedirectUrl(userRole);
        router.push(redirectUrl);
      }

      return result;
    } catch (err: any) {
      setError(err.message);
      return { user: null, session: null, error: err };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Регистрация нового пользователя
   *
   * @param email Email пользователя
   * @param password Пароль пользователя
   * @param metadata Дополнительные метаданные пользователя
   * @returns Результат операции регистрации
   */
  const signUp = async (email: string, password: string, metadata?: Record<string, any>) => {
    setError(null);
    setLoading(true);

    try {
      const result = await authService.signUp({
        email,
        password,
        role: metadata?.role || 'client',
        tenant_id: metadata?.tenant_id,
        first_name: metadata?.first_name,
        last_name: metadata?.last_name,
        phone: metadata?.phone,
      });

      if (result.error) {
        setError(result.error.message);
        return result;
      }

      return result;
    } catch (err: any) {
      setError(err.message);
      return { user: null, session: null, error: err };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Выход пользователя из системы
   *
   * @returns Результат операции выхода
   */
  const signOut = async () => {
    setError(null);
    setLoading(true);

    try {
      const result = await authService.signOut();

      if (result.error) {
        setError(result.error.message);
      } else {
        // Перенаправляем на главную страницу
        router.push('/');
      }

      return result;
    } catch (err: any) {
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Отправка ссылки для сброса пароля
   *
   * @param email Email пользователя
   * @returns Результат операции сброса пароля
   */
  const resetPassword = async (email: string) => {
    setError(null);

    try {
      const result = await authService.resetPassword(email);

      if (result.error) {
        setError(result.error.message);
      }

      return result;
    } catch (err: any) {
      setError(err.message);
      return { error: err };
    }
  };

  /**
   * Обновление пароля пользователя
   *
   * @param password Новый пароль
   * @returns Результат операции обновления пароля
   */
  const updatePassword = async (password: string) => {
    setError(null);

    try {
      const result = await authService.updatePassword(password);

      if (result.error) {
        setError(result.error.message);
      }

      return result;
    } catch (err: any) {
      setError(err.message);
      return { error: err };
    }
  };

  /**
   * Обновление метаданных пользователя
   *
   * @param metadata Новые метаданные
   * @returns Результат операции обновления метаданных
   */
  const updateUserMetadata = async (metadata: Record<string, any>) => {
    setError(null);

    try {
      const result = await supabase.auth.updateUser({
        data: metadata
      });

      if (result.error) {
        setError(result.error.message);
      }

      return result;
    } catch (err: any) {
      setError(err.message);
      return { error: err };
    }
  };

  // Дополнительные утилиты для работы с пользователем
  const getUserRole = () => userUtils.getRole(user);
  const getUserTenantId = () => userUtils.getTenantId(user);
  const getUserFullName = () => userUtils.getFullName(user);
  const hasRole = (role: string) => userUtils.hasRole(user, role);
  const isAdmin = () => userUtils.isAdmin(user);
  const isSuperAdmin = () => userUtils.isSuperAdmin(user);

  return {
    user,
    session,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePassword,
    updateUserMetadata,
    getUserRole,
    getUserTenantId,
    getUserFullName,
    hasRole,
    isAdmin,
    isSuperAdmin,
    supabase
  };
}
