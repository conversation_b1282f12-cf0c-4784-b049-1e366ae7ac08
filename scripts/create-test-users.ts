/**
 * @file: create-test-users.ts
 * @description: Скрипт для создания тестовых пользователей для демонстрации RBAC
 * @dependencies: @supabase/supabase-js
 * @created: 2025-01-26
 */

import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'

// Загружаем переменные окружения
config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Отсутствуют переменные окружения SUPABASE_URL или SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

// Создаем клиент с service role ключом для административных операций
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

/**
 * Интерфейс для тестового пользователя
 */
interface TestUser {
  email: string
  password: string
  role: string
  tenant_id?: string
  first_name: string
  last_name: string
  phone?: string
  description: string
}

/**
 * Тестовые пользователи для демонстрации RBAC
 */
const testUsers: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'SuperAdmin123!',
    role: 'superadmin',
    first_name: 'Супер',
    last_name: 'Администратор',
    phone: '+995555000001',
    description: 'Глобальный администратор платформы PactCRM'
  },
  {
    email: '<EMAIL>',
    password: 'Support123!',
    role: 'support',
    first_name: 'Техническая',
    last_name: 'Поддержка',
    phone: '+995555000002',
    description: 'Сотрудник технической поддержки'
  },
  {
    email: '<EMAIL>',
    password: 'TenantAdmin123!',
    role: 'tenant_admin',
    tenant_id: '00000000-0000-0000-0000-000000000001',
    first_name: 'Георгий',
    last_name: 'Застройщиков',
    phone: '+995555000003',
    description: 'Администратор компании-застройщика "Строй Инвест"'
  },
  {
    email: '<EMAIL>',
    password: 'Manager123!',
    role: 'after_sales_manager',
    tenant_id: '00000000-0000-0000-0000-000000000001',
    first_name: 'Анна',
    last_name: 'Менеджерова',
    phone: '+995555000004',
    description: 'Менеджер пост-продаж компании "Строй Инвест"'
  },
  {
    email: '<EMAIL>',
    password: 'Client123!',
    role: 'client',
    tenant_id: '00000000-0000-0000-0000-000000000001',
    first_name: 'Иван',
    last_name: 'Покупателев',
    phone: '+995555000005',
    description: 'Покупатель квартиры в ЖК "Солнечный"'
  },
  {
    email: '<EMAIL>',
    password: 'TenantAdmin123!',
    role: 'tenant_admin',
    tenant_id: '00000000-0000-0000-0000-000000000002',
    first_name: 'Давид',
    last_name: 'Девелоперов',
    phone: '+995555000006',
    description: 'Администратор компании-застройщика "Недвижимость+"'
  }
]

/**
 * Создает тестового пользователя
 */
async function createTestUser(user: TestUser): Promise<boolean> {
  try {
    console.log(`📝 Создание пользователя: ${user.email} (${user.role})`)

    // Создаем пользователя через Admin API
    const { data, error } = await supabase.auth.admin.createUser({
      email: user.email,
      password: user.password,
      email_confirm: true, // Автоматически подтверждаем email
      user_metadata: {
        role: user.role,
        tenant_id: user.tenant_id,
        first_name: user.first_name,
        last_name: user.last_name,
        phone: user.phone,
        description: user.description,
      }
    })

    if (error) {
      console.error(`❌ Ошибка создания пользователя ${user.email}:`, error.message)
      return false
    }

    console.log(`✅ Пользователь ${user.email} создан успешно`)
    return true
  } catch (error) {
    console.error(`❌ Исключение при создании пользователя ${user.email}:`, error)
    return false
  }
}

/**
 * Создает тестовый tenant (компанию-застройщика)
 */
async function createTestTenant(id: string, name: string, description: string): Promise<boolean> {
  try {
    console.log(`🏢 Создание тестового tenant: ${name}`)

    const { error } = await supabase
      .from('tenants')
      .insert({
        id,
        name,
        description,
        status: 'active',
        settings: {
          currency: 'GEL',
          timezone: 'Asia/Tbilisi',
          language: 'ka'
        }
      })

    if (error) {
      console.error(`❌ Ошибка создания tenant ${name}:`, error.message)
      return false
    }

    console.log(`✅ Tenant ${name} создан успешно`)
    return true
  } catch (error) {
    console.error(`❌ Исключение при создании tenant ${name}:`, error)
    return false
  }
}

/**
 * Основная функция
 */
async function main() {
  console.log('🚀 Начинаем создание тестовых данных для PactCRM...\n')

  // Создаем тестовые tenants
  const tenants = [
    {
      id: '00000000-0000-0000-0000-000000000001',
      name: 'ООО "Строй Инвест"',
      description: 'Компания-застройщик, специализирующаяся на жилых комплексах'
    },
    {
      id: '00000000-0000-0000-0000-000000000002',
      name: 'ГК "Недвижимость+"',
      description: 'Группа компаний в сфере недвижимости и девелопмента'
    }
  ]

  console.log('📋 Создание тестовых компаний-застройщиков...')
  for (const tenant of tenants) {
    await createTestTenant(tenant.id, tenant.name, tenant.description)
  }

  console.log('\n👥 Создание тестовых пользователей...')
  let successCount = 0
  let totalCount = testUsers.length

  for (const user of testUsers) {
    const success = await createTestUser(user)
    if (success) successCount++
    
    // Небольшая пауза между созданием пользователей
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  console.log('\n📊 Результаты создания тестовых данных:')
  console.log(`✅ Успешно создано пользователей: ${successCount}/${totalCount}`)
  
  if (successCount === totalCount) {
    console.log('\n🎉 Все тестовые данные созданы успешно!')
    console.log('\n📝 Данные для входа:')
    console.log('┌─────────────────────────────┬──────────────────┬─────────────────┐')
    console.log('│ Email                       │ Пароль           │ Роль            │')
    console.log('├─────────────────────────────┼──────────────────┼─────────────────┤')
    
    testUsers.forEach(user => {
      console.log(`│ ${user.email.padEnd(27)} │ ${user.password.padEnd(16)} │ ${user.role.padEnd(15)} │`)
    })
    
    console.log('└─────────────────────────────┴──────────────────┴─────────────────┘')
  } else {
    console.log('\n⚠️  Некоторые пользователи не были созданы. Проверьте логи выше.')
  }
}

// Запускаем скрипт
main()
  .then(() => {
    console.log('\n✨ Скрипт завершен')
    process.exit(0)
  })
  .catch((error) => {
    console.error('\n💥 Критическая ошибка:', error)
    process.exit(1)
  })
